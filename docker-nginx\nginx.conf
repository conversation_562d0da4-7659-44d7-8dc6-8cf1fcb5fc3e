user nobody;

worker_processes  auto;

events {
    worker_connections  1024;
    multi_accept on;
}

http {
    include mime.types;

    default_type application/octet-stream;

    server_tokens off;

    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    keepalive_requests 100;
    types_hash_max_size 2048;

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers EECDH+ECDSA+AESGCM:EECDH+aRSA+AESGCM:EECDH+ECDSA+SHA512:EECDH+ECDSA+SHA384:EECDH+ECDSA+SHA256:ECDH+AESGCM:ECDH+AES256:DH+AESGCM:DH+AES256:RSA+AESGCM:!aNULL:!eNULL:!LOW:!RC4:!3DES:!MD5:!EXP:!PSK:!SRP:!DSS;

    ssl_session_cache shared:TLS:2m;
    ssl_stapling on;
    ssl_stapling_verify on;
    resolver ******* [2606:4700:4700::1111] ******* [2606:4700:4700::1001] ipv6=on valid=300s;
    resolver_timeout 60s;

    add_header Strict-Transport-Security 'max-age=31536000; preload' always;

    gzip on;
    gzip_vary on;
    gzip_comp_level 4;
    gzip_types text/plain text/css text/xml text/javascript application/json application/x-javascript application/xml;

    access_log /dev/stdout;
    error_log  /dev/stdout;

    include /etc/nginx/conf.d/*.conf;
}
