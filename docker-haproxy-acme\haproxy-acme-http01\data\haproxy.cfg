global
  log stdout format raw local0
  # Allow 'acme.sh' to deploy new certificates without reloading
  stats socket /var/lib/haproxy/admin.sock level admin mode 660
  maxconn  100000
  nbthread 4
  ssl-default-bind-ciphersuites TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256
  ssl-default-bind-options prefer-client-ciphers no-sslv3 no-tlsv10 no-tlsv11 no-tlsv12 no-tls-tickets
  ssl-default-server-ciphersuites TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256
  ssl-default-server-options no-sslv3 no-tlsv10 no-tlsv11 no-tlsv12 no-tls-tickets
  ssl-server-verify none
  tune.ssl.default-dh-param 4096
  tune.bufsize 65536

defaults
  maxconn                 10000
  retries                 3
  timeout http-request    10s
  timeout queue           1m
  timeout connect         10s
  timeout client          1m
  timeout server          1m
  timeout http-keep-alive 10s
  timeout check           10s
  log                     global
  option httplog
  option dontlognull

frontend http
  mode http
  bind ":${HAPROXY_HTTP_PORT}" name http
  # Respond to ACME HTTP-01 challenge
  http-request return status 200 content-type text/plain lf-string "%[path,field(-1,/)].${ACME_ACCOUNT_THUMBPRINT}\n" if { path_beg '/.well-known/acme-challenge/' }
  # Redirect to HTTPS
  http-request redirect scheme https

frontend https
  mode http
  bind ":${HAPROXY_HTTPS_PORT}" name https ssl crt /etc/haproxy/certs/ strict-sni alpn h2,http/1.1
  option forwardfor if-none
  default_backend main

resolvers default
  parse-resolv-conf

backend main
  mode http
  server main "${SERVER_ADDRESS}:${SERVER_PORT}" "$SERVER_DIRECTIVES" resolvers default
