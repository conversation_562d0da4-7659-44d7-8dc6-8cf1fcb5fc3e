---
name: Upgrade Makesystem

on:
  schedule:
    - cron: '0 */3 * * *'
  workflow_dispatch:

permissions:
  contents: write
  pull-requests: write

jobs:
  upgrade-makesystem:
    name: Upgrade Makesystem
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Fetch full annotated tags metadata
        run: git fetch --force --tags

      - name: Set up makesystem
        run: make makesystem_install

      - name: Upgrade makesystem locally
        run: make makesystem_upgrade

      - name: Dump makesystem version
        id: dump-makesystem-version
        run: make github_dump_makesystem_version

      - uses: tibdex/github-app-token@v2
        id: generate-token
        with:
          app_id: ${{ secrets.UPGRADE_MAKESYSTEM_BOT_APP_ID }}
          private_key: ${{ secrets.UPGRADE_MAKESYSTEM_BOT_APP_PRIVATE_KEY }}

      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v7
        with:
          token: ${{ steps.generate-token.outputs.token }}
          commit-message: |
            build: [upgrade-makesystem-bot] Upgrade to v${{ steps.dump-makesystem-version.outputs.version }}.

            Automated change generated by the Upgrade Makesystem Bot.
          branch: upgrade-makesystem-bot/patch
          delete-branch: true
          title: |
            Upgrade the makesystem to v${{ steps.dump-makesystem-version.outputs.version }}
          body: |
            Automated change generated by the Upgrade Makesystem Bot.
