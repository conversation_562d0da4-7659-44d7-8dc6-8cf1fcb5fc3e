name: <PERSON>uild and Push Docker Image

on:
  push:
    branches:
      - main
      - dev
    paths-ignore:
      - 'readme.md'
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        base_tag: [latest, alpine, bookworm]

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Replace base image in Dockerfile
        run: sed -i "s#docker.io/nginx:latest#docker.io/nginx:${{ matrix.base_tag }}#g" dockerfile

      - name: Install bash for Alpine
        if: ${{ matrix.base_tag == 'alpine' }}
        run: sed -i '/^FROM/ a RUN apk add --no-cache bash openssl' dockerfile

      - name: Determine the Docker tag
        id: get_tag
        env:
          BASE_TAG: ${{ matrix.base_tag }}
        run: |
          if [ "${{ github.ref }}" == "refs/heads/main" ]; then
            echo "::set-output name=tag::ghcr.io/${{ github.repository }}:${BASE_TAG}"
          elif [ "${{ github.ref }}" == "refs/heads/dev" ]; then
            echo "::set-output name=tag::ghcr.io/${{ github.repository }}:${{ github.sha }}-${BASE_TAG}"
          fi

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./dockerfile
          push: true
          tags: ${{ steps.get_tag.outputs.tag }}
          platforms: linux/amd64,linux/arm64,linux/arm/v7

