---
name: Lint

on:
  push:
    branches:
      - master
    tags:
      - v*
    paths-ignore:
      - '**.md'
  pull_request:
    # The branches below must be a subset of the branches above
    branches:
      - master
    paths-ignore:
      - '**.md'
  schedule:
    - cron: '00 03 * * *'
  workflow_dispatch:

permissions:
  contents: read

jobs:
  build:
    name: Lint
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Fetch full annotated tags metadata
        run: git fetch --force --tags

      - name: Set up makesystem
        run: make makesystem_install

      - name: Run hadolint
        uses: hadolint/hadolint-action@v3.1.0
        with:
          dockerfile: Dockerfile
