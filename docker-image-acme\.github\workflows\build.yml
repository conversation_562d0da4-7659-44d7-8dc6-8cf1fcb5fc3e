---
name: Build

on:
  push:
    branches:
      - master
    tags:
      - v*
    paths-ignore:
      - '**.md'
  pull_request:
    # The branches below must be a subset of the branches above
    branches:
      - master
    paths-ignore:
      - '**.md'
  schedule:
    - cron: '00 03 * * *'
  workflow_dispatch:

permissions:
  contents: read
  packages: write

env:
  DOCKERHUB_TEST_TAG: ci-test

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    timeout-minutes: 720

    strategy:
      fail-fast: false

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Fetch full annotated tags metadata
        run: git fetch --force --tags

      - name: Configure DockerHub Publish Image Tag
        run: |
          github_ref="${{github.ref}}"
          if [[ "${github_ref:?}" == refs/tags/v* ]]; then
            echo "DOCKERHUB_PUBLISH_TAG=${github_ref#refs/tags/v}" >> $GITHUB_ENV
          else
            echo "DOCKERHUB_PUBLISH_TAG=${{github.ref_name}}" >> $GITHUB_ENV
          fi

      - name: Set up makesystem
        run: make makesystem_install

      - name: Show Docker version
        run: |
          docker version
          docker info

      - name: Set Up Docker Build Kit Daemon Flags
        id: docker_buildkitd_flags
        run: make github_dump_docker_buildkitd_flags

      - name: Set Up Docker Entitlements
        id: docker_entitlements
        run: make github_dump_docker_entitlements

      - name: Set Up Docker Build Args
        id: docker_build_args
        run: make github_dump_docker_build_args

      - name: Set Workflow Env vars
        run: make github_env_vars >> $GITHUB_ENV

      - name: Set up QEMU for Docker
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          buildkitd-flags: ${{ steps.docker_buildkitd_flags.outputs.buildkitd_flags }}

      - name: Login to DockerHub
        uses: docker/login-action@v3
        if: >-
          (
            github.event_name != 'pull_request'
          ) && (
            github.ref_name == 'master' ||
            startsWith(github.ref, 'refs/tags/v')
          )
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build amd64 Docker image locally (with future registry pushes)
        uses: docker/build-push-action@v6
        if: >-
          (
            github.event_name != 'pull_request'
          ) && (
            github.ref_name == 'master' ||
            startsWith(github.ref, 'refs/tags/v')
          )
        with:
          context: .
          load: true
          push: false
          tags: ${{ env.DOCKERHUB_REPO_NAME }}:${{ env.DOCKERHUB_TEST_TAG }}
          build-args: ${{ steps.docker_build_args.outputs.build_args }}
          allow: ${{ steps.docker_entitlements.outputs.entitlements }}
          cache-from: type=gha,scope=${{ github.workflow }}
          cache-to: type=gha,scope=${{ github.workflow }},mode=max

      - name: Build amd64 Docker image locally (without future registry pushes)
        uses: docker/build-push-action@v6
        if: >-
          (
            github.event_name == 'pull_request'
          ) || (
            github.ref_name != 'master' &&
            !startsWith(github.ref, 'refs/tags/v')
          )
        with:
          context: .
          load: true
          push: false
          tags: ${{ env.DOCKERHUB_REPO_NAME }}:${{ env.DOCKERHUB_TEST_TAG }}
          build-args: ${{ steps.docker_build_args.outputs.build_args }}
          allow: ${{ steps.docker_entitlements.outputs.entitlements }}
          cache-from: type=gha,scope=${{ github.workflow }}

      - name: Test the local amd64 Docker image
        run: |
          IMAGE_TAG=${{ env.DOCKERHUB_TEST_TAG }} make test

      - name: Build docker images for all platforms (with registry pushes)
        uses: docker/build-push-action@v6
        if: >-
          (
            github.event_name != 'pull_request'
          ) && (
            github.ref_name == 'master' ||
            startsWith(github.ref, 'refs/tags/v')
          )
        with:
          context: .
          platforms: ${{env.IMAGE_SUPPORTED_DOCKER_PLATFORMS}}
          push: true
          provenance: mode=max
          sbom: true
          tags: |
            ${{ env.DOCKERHUB_REPO_NAME }}:${{ env.DOCKERHUB_PUBLISH_TAG }}
            ${{ env.GHCR_REPO_NAME }}:${{ env.DOCKERHUB_PUBLISH_TAG }}
          build-args: ${{ steps.docker_build_args.outputs.build_args }}
          allow: ${{ steps.docker_entitlements.outputs.entitlements }}
          cache-from: type=gha,scope=${{ github.workflow }}
          cache-to: type=gha,scope=${{ github.workflow }},mode=max

      - name: Build docker images for all platforms (no registry pushes)
        uses: docker/build-push-action@v6
        if: >-
          (
            github.event_name == 'pull_request'
          ) || (
            github.ref_name != 'master' &&
            !startsWith(github.ref, 'refs/tags/v')
          )
        with:
          context: .
          platforms: ${{env.IMAGE_SUPPORTED_DOCKER_PLATFORMS}}
          push: false
          tags: ${{ env.DOCKERHUB_REPO_NAME }}:${{ env.DOCKERHUB_TEST_TAG }}
          build-args: ${{ steps.docker_build_args.outputs.build_args }}
          allow: ${{ steps.docker_entitlements.outputs.entitlements }}
          cache-from: type=gha,scope=${{ github.workflow }}
