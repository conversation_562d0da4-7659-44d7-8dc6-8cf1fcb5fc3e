name: CD

permissions:
  contents: read
  packages: write

on:
  workflow_call:
    inputs:
      registry:
        description: The registry name
        required: false
        type: string
        default: ''
      image:
        description: The image name (without registry)
        required: true
        type: string
      context:
        description: The build context
        required: true
        type: string
      build-args:
        description: The build arguments
        required: false
        type: string
        default: ''
      platforms:
        description: A JSON encoded array of the target platforms
        required: false
        type: string
        default: '["linux/amd64"]'
      tags:
        description: The build tags (with registry)
        required: true
        type: string
      labels:
        description: The build labels
        required: false
        type: string
        default: ''
      annotations:
        description: The build annotations
        required: false
        type: string
        default: ''

    secrets:
      REGISTRY_USERNAME:
        description: The Docker registry username
        required: false
      REGISTRY_PASSWORD:
        description: The Docker registry password
        required: false

env:
  REGISTRY_IMAGE: ${{ inputs.registry == '' && inputs.image || format('{0}/{1}', inputs.registry, inputs.image) }}

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        platform: ${{ fromJson(inputs.platforms) }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ inputs.registry }}
          username: ${{ secrets.REGISTRY_USERNAME }}
          password: ${{ secrets.REGISTRY_PASSWORD }}

      - name: Build and Push by Digest
        id: build
        uses: docker/build-push-action@v5
        with:
          build-args: ${{ inputs.build-args }}
          context: ${{ inputs.context }}
          platforms: ${{ matrix.platform }}
          labels: ${{ inputs.labels }}
          annotations: ${{ inputs.annotations }}
          outputs: type=image,name=${{ env.REGISTRY_IMAGE }},push-by-digest=true,name-canonical=true,push=true
          provenance: false

      - name: Artifact Name
        env:
          IMAGE_ID: ${{ inputs.image }}
        run: |
          echo "IMAGE_ID=${IMAGE_ID////-}" >> $GITHUB_ENV
          platform=${{ matrix.platform }}
          echo "PLATFORM_PAIR=${platform//\//-}" >> $GITHUB_ENV

      - name: Export Digest
        run: |
          mkdir -p /tmp/digests
          digest="${{ steps.build.outputs.digest }}"
          touch "/tmp/digests/${digest#sha256:}"

      - name: Upload Digest
        uses: actions/upload-artifact@v4
        with:
          name: digests-${{ env.IMAGE_ID }}-${{ env.PLATFORM_PAIR }}
          path: /tmp/digests/*
          if-no-files-found: error
          retention-days: 1

  merge:
    name: Merge '${{ inputs.image }}'
    runs-on: ubuntu-latest

    needs:
      - build

    steps:
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Artifact Name
        env:
          IMAGE_ID: ${{ inputs.image }}
        run: |
          echo "IMAGE_ID=${IMAGE_ID////-}" >> $GITHUB_ENV

      - name: Download Digests
        uses: actions/download-artifact@v4
        with:
          path: /tmp/digests
          pattern: digests-${{ env.IMAGE_ID }}-*
          merge-multiple: true

      - name: Login to Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ inputs.registry }}
          username: ${{ secrets.REGISTRY_USERNAME }}
          password: ${{ secrets.REGISTRY_PASSWORD }}

      - name: Create Manifest List and Push
        working-directory: /tmp/digests
        run: |
          readarray -t tags <<<"${{ inputs.tags }}"

          shopt -s extglob
          tags=( "${tags[@]/#+([[:blank:]])/}" ) # remove leading whitespaces
          tags=( "${tags[@]/%+([[:blank:]])/}" ) # remove trailing whitespaces

          args=(
              "buildx"
              "imagetools"
              "create"
          )

          for t in "${tags[@]}"
          do
              if [[ -z "$t" ]]; then
                  continue
              fi

              args+=("-t" "$t")
          done

          for d in *
          do
              args+=("${REGISTRY_IMAGE}@sha256:$d")
          done

          echo "${args[@]}"
          docker "${args[@]}"
