server
{   listen 80;
    server_name MYWEBD;                   #############################
    index index.php index.html index.htm default.php default.htm default.html;
    root /usr/share/nginx/html/my.web.com;

    #SSL-START SSL相关配置，请勿删除或修改下一行带注释的404规则
    #error_page 404/404.html;
    #HTTP_TO_HTTPS_START

    #HTTP_TO_HTTPS_END
    # limit_conn perserver 300;
    # limit_conn perip 35;

		#SSL-END

    #ERROR-PAGE-START  错误页配置，可以注释、删除或修改
    #error_page 404 /404.html;
    #error_page 502 /502.html;
    #ERROR-PAGE-END

    #禁止访问的文件或目录
    location ~ ^/(\.user.ini|\.htaccess|\.git|\.env|\.svn|\.project|LICENSE|README.md)
    {
        return 404;
    }

    #一键申请SSL证书验证目录相关设置
  location ~ ^/\.well-known {
    proxy_pass http://*************:80;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  }

    #禁止在证书验证目录放入敏感文件
    if ( $uri ~ "^/\.well-known/.*\.(php|jsp|py|js|css|lua|ts|go|zip|tar\.gz|rar|7z|sql|bak)$" ) {
        return 403;
    }

  
location /robots.txt {
    default_type text/plain;
    return 200 "User-agent: *\nDisallow: /\n";
}

  location ~^/ssV {
    proxy_pass http://127.0.0.1:54339;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  }

  
location ~^/wshttpup(?<port>\d+) { #与 VLESS+HTTPUpgrade 应用中 path 对应
    if ($http_upgrade != "websocket") {
        return 404;
    }
    proxy_http_version 1.1;
    proxy_pass http://127.0.0.1:$port; #转发给本机 VLESS+HTTPUpgrade 监听端口
    proxy_redirect off;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
}


location ~^/xhttpgrpc(?<port>\d+) { #与 VLESS+XHTTP 应用中 path 对应
    grpc_pass grpc://127.0.0.1:$port; #转发给本机 VLESS+XHTTP 监听进程
    grpc_set_header Host $host;
    grpc_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
}

    access_log  /usr/share/nginx/html/MYWEBD.log;
    error_log  /usr/share/nginx/html/MYWEBD.error.log;
}