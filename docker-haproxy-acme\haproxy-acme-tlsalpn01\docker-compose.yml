services:
  haproxy-acme:
    image: flobernd/haproxy-acme-tlsalpn01:latest
    container_name: haproxy-acme-tlsalpn01
    restart: unless-stopped
    environment:
      - "HAPROXY_HTTP_PORT=80"
      - "HAPROXY_HTTPS_PORT=443"
      - "HAPROXY_HTTPS_REASSIGN_PORT=8443"
      - "ACME_DEBUG=0"
      - "ACME_UPGRADE=1"
      - "ACME_CRON=1"
      - "ACME_MAIL=<EMAIL>"
      - "ACME_SERVER=letsencrypt_test"
      - "ACME_DOMAIN=domain.com"
      - "ACME_KEYLENGTH=ec-256"
      - "ACME_TLSALPN_PORT=10433"
      - "SERVER_ADDRESS=whoami"
      - "SERVER_PORT=80"
      - "SERVER_DIRECTIVES="
    volumes:
      - ./volume/acme:/var/lib/acme:rw
    ports:
      - 80:80
      - 443:443

  whoami:
    image: traefik/whoami
    container_name: whoami
    restart: unless-stopped
