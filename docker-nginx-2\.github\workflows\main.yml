name: Main Branch CI

# For all pushes to the main branch run the tests and push the image to the
# GitHub registry under an edge tag so we can use it for the nightly
# integration tests
on:
  push:
    branches: ['master']

jobs:
  docker:
    runs-on: ubuntu-latest
    steps:
      # GitHub Actions do not automatically checkout your projects. If you need the code
      # you need to check it out.
      - name: Checkout
        uses: actions/checkout@v3

      - name: Prepare
        id: prep
        run: |
          GHCR_IMAGE=ghcr.io/epoweripione/nginx
          ACR_IMAGE=registry.cn-guangzhou.aliyuncs.com/siyuwuxin/nginx
          VERSION=edge
          if [[ $GITHUB_REF == refs/tags/* ]]; then
            VERSION=${GITHUB_REF#refs/tags/v}
          fi
          if [ "${{ github.event_name }}" = "schedule" ]; then
            VERSION=nightly
          fi
          TAGS="${GHCR_IMAGE}:${VERSION}"
          TAGS="$TAGS,${GHCR_IMAGE}:latest,${ACR_IMAGE}:latest"
          echo ::set-output name=tags::${TAGS}

      - name: Setup QEMU
        id: qemu
        uses: docker/setup-qemu-action@v2
        with:
          platforms: amd64,arm64

      - name: Setup Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v2

      - name: Cache Docker layers
        uses: actions/cache@v3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      - name: Login to ghcr
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.PACKAGES_TOKEN }}

      - name: Login to ACR
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v2
        with:
          registry: registry.cn-guangzhou.aliyuncs.com/siyuwuxin/nginx
          username: ${{ secrets.ACR_USERNAME }}
          password: ${{ secrets.ACR_PASSWORD }}

      - name: Build and push
        id: docker_build
        uses: docker/build-push-action@v3
        with:
          builder: ${{ steps.buildx.outputs.name }}
          context: .
          file: ./Dockerfile
          # target: prod
          # platforms: linux/386,linux/amd64,linux/arm/v6,linux/arm/v7,linux/arm64,linux/ppc64le,linux/s390x
          platforms: linux/amd64,linux/arm64
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.prep.outputs.tags }}
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache

      - name: Image digest
        run: echo ${{ steps.docker_build.outputs.digest }}
