#!/bin/bash

# Certificate issuance script for nginx-acme setup
# Usage: ./scripts/issue-cert.sh domain.com [dns_provider]

set -e

DOMAIN="$1"
DNS_PROVIDER="$2"

if [ -z "$DOMAIN" ]; then
    echo "Usage: $0 <domain> [dns_provider]"
    echo ""
    echo "Examples:"
    echo "  $0 example.com                    # HTTP-01 challenge"
    echo "  $0 example.com dns_cf             # DNS-01 challenge with Cloudflare"
    echo "  $0 example.com dns_aws            # DNS-01 challenge with AWS Route53"
    echo ""
    echo "Supported DNS providers:"
    echo "  dns_cf      - Cloudflare"
    echo "  dns_aws     - AWS Route53"
    echo "  dns_gce     - Google Cloud DNS"
    echo "  dns_do      - DigitalOcean"
    echo "  dns_namecheap - Namecheap"
    echo "  dns_godaddy - GoDaddy"
    echo ""
    exit 1
fi

echo "Issuing certificate for domain: $DOMAIN"

# Check if containers are running
if ! docker ps | grep -q "acme-sh"; then
    echo "❌ acme-sh container is not running. Please start with: docker-compose up -d"
    exit 1
fi

if ! docker ps | grep -q "nginx-acme"; then
    echo "❌ nginx-acme container is not running. Please start with: docker-compose up -d"
    exit 1
fi

# Create SSL directory for the domain
echo "Creating SSL directory for $DOMAIN..."
docker exec nginx-acme mkdir -p "/etc/nginx/ssl/$DOMAIN"

if [ -n "$DNS_PROVIDER" ]; then
    echo "Using DNS-01 challenge with provider: $DNS_PROVIDER"
    
    # Issue certificate using DNS challenge
    docker exec acme-sh acme.sh --issue -d "$DOMAIN" --dns "$DNS_PROVIDER"
    
else
    echo "Using HTTP-01 challenge (webroot method)"
    
    # Ensure webroot directory exists
    docker exec nginx-acme mkdir -p /usr/share/nginx/html/.well-known/acme-challenge
    
    # Issue certificate using HTTP challenge
    docker exec acme-sh acme.sh --issue -d "$DOMAIN" --webroot /usr/share/nginx/html
fi

# Install certificate to nginx SSL directory
echo "Installing certificate to nginx..."
docker exec acme-sh acme.sh --install-cert -d "$DOMAIN" \
    --fullchain-file "/nginx-ssl/$DOMAIN/fullchain.pem" \
    --key-file "/nginx-ssl/$DOMAIN/privkey.pem" \
    --reloadcmd "docker exec nginx-acme nginx -s reload"

# Verify certificate installation
if docker exec nginx-acme test -f "/etc/nginx/ssl/$DOMAIN/fullchain.pem"; then
    echo "✅ Certificate successfully installed for $DOMAIN"
    echo ""
    echo "Certificate files:"
    echo "  - /etc/nginx/ssl/$DOMAIN/fullchain.pem"
    echo "  - /etc/nginx/ssl/$DOMAIN/privkey.pem"
    echo ""
    echo "Next steps:"
    echo "1. Update your nginx configuration to use these certificate paths"
    echo "2. Reload nginx: docker exec nginx-acme nginx -s reload"
    echo "3. Test your SSL configuration: https://$DOMAIN"
else
    echo "❌ Certificate installation failed"
    exit 1
fi

# Show certificate info
echo ""
echo "Certificate information:"
docker exec acme-sh acme.sh --info -d "$DOMAIN"
