#!/bin/bash

# Setup script for nginx-acme docker-compose environment
# This script initializes the directory structure and generates default SSL certificates

set -e

echo "Setting up nginx-acme environment..."

# Create necessary directories
echo "Creating directory structure..."
mkdir -p nginx/conf.d
mkdir -p nginx/html
mkdir -p nginx/ssl
mkdir -p logs/nginx
mkdir -p volumes/acme-data

# Create default index.html
echo "Creating default index.html..."
cat > nginx/html/index.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>Welcome to nginx-acme!</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 600px; margin: 0 auto; }
        .status { padding: 10px; border-radius: 5px; margin: 10px 0; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Welcome to nginx-acme!</h1>
        <div class="status success">
            ✅ Nginx is running successfully
        </div>
        <div class="status info">
            ℹ️ This is the default page. Configure your domains in the nginx/conf.d/ directory.
        </div>
        <h2>Next Steps:</h2>
        <ol>
            <li>Copy <code>nginx/conf.d/example.conf.template</code> to <code>nginx/conf.d/yourdomain.conf</code></li>
            <li>Update the server_name and SSL certificate paths</li>
            <li>Issue SSL certificates using acme.sh</li>
            <li>Restart nginx to load the new configuration</li>
        </ol>
        <h2>Certificate Management:</h2>
        <p>Use the following commands to manage SSL certificates:</p>
        <pre>
# Issue a certificate (HTTP-01 challenge)
docker exec acme-sh --issue -d yourdomain.com --webroot /usr/share/nginx/html

# Issue a certificate (DNS-01 challenge with Cloudflare)
docker exec acme-sh --issue -d yourdomain.com --dns dns_cf

# Install certificate to nginx directory
docker exec acme-sh --install-cert -d yourdomain.com \
  --fullchain-file /nginx-ssl/yourdomain.com/fullchain.pem \
  --key-file /nginx-ssl/yourdomain.com/privkey.pem \
  --reloadcmd "docker exec nginx-acme nginx -s reload"

# List all certificates
docker exec acme-sh --list

# Renew certificates (automatic via cron)
docker exec acme-sh --cron
        </pre>
    </div>
</body>
</html>
EOF

# Generate self-signed default certificate for nginx startup
echo "Generating self-signed default certificate..."
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout nginx/ssl/default.key \
    -out nginx/ssl/default.crt \
    -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"

# Set proper permissions
echo "Setting permissions..."
chmod 600 nginx/ssl/default.key
chmod 644 nginx/ssl/default.crt
chmod +x scripts/setup.sh

# Copy environment file if it doesn't exist
if [ ! -f .env ]; then
    echo "Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please edit .env file with your domain and email settings"
fi

echo "✅ Setup complete!"
echo ""
echo "Next steps:"
echo "1. Edit .env file with your domain and email"
echo "2. Copy nginx/conf.d/example.conf.template to nginx/conf.d/yourdomain.conf"
echo "3. Update the configuration with your domain name"
echo "4. Run: docker-compose up -d"
echo "5. Issue SSL certificates using the acme-sh container"
echo ""
echo "For more information, see the README.md file."
