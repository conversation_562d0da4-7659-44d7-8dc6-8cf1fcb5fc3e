global
  log stdout format raw local0
  # Allow 'acme.sh' to deploy new certificates without reloading
  stats socket /var/lib/haproxy/admin.sock level admin mode 660
  maxconn  100000
  nbthread 4
  ssl-default-bind-ciphersuites TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256
  ssl-default-bind-options prefer-client-ciphers no-sslv3 no-tlsv10 no-tlsv11 no-tlsv12 no-tls-tickets
  ssl-default-server-ciphersuites TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256
  ssl-default-server-options no-sslv3 no-tlsv10 no-tlsv11 no-tlsv12 no-tls-tickets
  ssl-server-verify none
  tune.ssl.default-dh-param 4096
  tune.bufsize 65536

defaults
  maxconn                 10000
  retries                 3
  timeout http-request    10s
  timeout queue           1m
  timeout connect         10s
  timeout client          1m
  timeout server          1m
  timeout http-keep-alive 10s
  timeout check           10s
  log                     global
  option httplog
  option dontlognull

frontend http
  mode http
  bind ":${HAPROXY_HTTP_PORT}" name http
  # Redirect to HTTPS
  http-request redirect scheme https

# TCP frontend on 443 to load balance ALPN
frontend fe_alpn
  mode tcp
  option tcplog
  bind ":${HAPROXY_HTTPS_PORT}" name bind_fe_alpn
  tcp-request inspect-delay 5s
  tcp-request content accept if { req_ssl_hello_type 1 }
  use_backend bk_acmesh if { req.ssl_alpn acme-tls/1 }
  default_backend bk_https

# A backend to send requests to acme.sh
backend bk_acmesh
  server acmesh "127.0.0.1:${ACME_TLSALPN_PORT}"

# A backend to send requests to the regular HTTPS frontend. Use PROXY protocol to retain the original clients source IP
backend bk_https
  server https "127.0.0.1:${HAPROXY_HTTPS_REASSIGN_PORT}" send-proxy-v2 

frontend https
  mode http
  bind ":${HAPROXY_HTTPS_REASSIGN_PORT}" name bind_fe_https ssl crt /etc/haproxy/certs/ strict-sni accept-proxy alpn h2,http/1.1 
  option forwardfor if-none
  default_backend main

resolvers default
  parse-resolv-conf

backend main
  mode http
  server main "${SERVER_ADDRESS}:${SERVER_PORT}" "$SERVER_DIRECTIVES" resolvers default
