[supervisord]
nodaemon = true
logfile = /dev/null
logfile_maxbytes = 0
pidfile = /tmp/supervisord.pid

[program:cron]
command = /usr/local/bin/cron.sh
priority = 1
stdout_logfile = /dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile = /dev/stderr
stderr_logfile_maxbytes=0
autostart = true
autorestart = true

[program:haproxy]
command = /usr/local/sbin/haproxy -W -f /usr/local/etc/haproxy/haproxy.cfg -f /usr/local/etc/haproxy/include
priority = 1
directory = /var/lib/haproxy
stopsignal = USR1
stdout_logfile = /dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile = /dev/stderr
stderr_logfile_maxbytes=0
autostart = true
autorestart = true

[program:acmeinit]
command = /bin/bash -c "sleep 5 && exec /usr/local/bin/acmeinit.late.sh"
priority = 2
stdout_logfile = /dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile = /dev/stderr
stderr_logfile_maxbytes=0
autostart = true
autorestart = false
