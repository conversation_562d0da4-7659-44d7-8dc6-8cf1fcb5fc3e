# Makefile for nginx-acme docker-compose setup

.PHONY: help setup up down restart logs clean cert-issue cert-list cert-renew test

# Default target
help: ## Show this help message
	@echo "Available commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

setup: ## Initial setup - create directories and default files
	@echo "Setting up nginx-acme environment..."
	@./scripts/setup.sh

up: ## Start all services
	@echo "Starting nginx-acme services..."
	@docker-compose up -d

down: ## Stop all services
	@echo "Stopping nginx-acme services..."
	@docker-compose down

restart: ## Restart all services
	@echo "Restarting nginx-acme services..."
	@docker-compose restart

logs: ## Show logs for all services
	@docker-compose logs -f

logs-nginx: ## Show nginx logs only
	@docker-compose logs -f nginx-acme

logs-acme: ## Show acme.sh logs only
	@docker-compose logs -f acme-sh

status: ## Show container status
	@docker-compose ps

cert-issue: ## Issue certificate for domain (usage: make cert-issue DOMAIN=example.com [DNS=dns_cf])
	@if [ -z "$(DOMAIN)" ]; then \
		echo "Usage: make cert-issue DOMAIN=example.com [DNS=dns_cf]"; \
		echo "Example: make cert-issue DOMAIN=example.com"; \
		echo "Example: make cert-issue DOMAIN=example.com DNS=dns_cf"; \
		exit 1; \
	fi
	@./scripts/issue-cert.sh $(DOMAIN) $(DNS)

cert-list: ## List all certificates
	@docker exec acme-sh acme.sh --list

cert-info: ## Show certificate info (usage: make cert-info DOMAIN=example.com)
	@if [ -z "$(DOMAIN)" ]; then \
		echo "Usage: make cert-info DOMAIN=example.com"; \
		exit 1; \
	fi
	@docker exec acme-sh acme.sh --info -d $(DOMAIN)

cert-renew: ## Force certificate renewal (usage: make cert-renew DOMAIN=example.com)
	@if [ -z "$(DOMAIN)" ]; then \
		echo "Usage: make cert-renew DOMAIN=example.com"; \
		exit 1; \
	fi
	@docker exec acme-sh acme.sh --renew -d $(DOMAIN) --force

cert-cron: ## Run certificate renewal cron job manually
	@docker exec acme-sh acme.sh --cron

nginx-reload: ## Reload nginx configuration
	@docker exec nginx-acme nginx -s reload

nginx-test: ## Test nginx configuration
	@docker exec nginx-acme nginx -t

test-ssl: ## Test SSL configuration (usage: make test-ssl DOMAIN=example.com)
	@if [ -z "$(DOMAIN)" ]; then \
		echo "Usage: make test-ssl DOMAIN=example.com"; \
		exit 1; \
	fi
	@echo "Testing SSL configuration for $(DOMAIN)..."
	@curl -I https://$(DOMAIN) || echo "HTTPS test failed"
	@echo "Certificate details:"
	@openssl s_client -connect $(DOMAIN):443 -servername $(DOMAIN) -verify_return_error < /dev/null 2>/dev/null | openssl x509 -noout -dates -subject -issuer

clean: ## Clean up containers and volumes (WARNING: This will remove all data)
	@echo "WARNING: This will remove all containers, volumes, and data!"
	@read -p "Are you sure? [y/N] " -n 1 -r; \
	if [[ $$REPLY =~ ^[Yy]$$ ]]; then \
		echo; \
		docker-compose down -v --remove-orphans; \
		docker volume prune -f; \
		echo "Cleanup complete."; \
	else \
		echo; \
		echo "Cleanup cancelled."; \
	fi

update: ## Update container images
	@echo "Updating container images..."
	@docker-compose pull
	@docker-compose up -d

backup: ## Backup certificates and configuration
	@echo "Creating backup..."
	@mkdir -p backups
	@tar -czf backups/nginx-acme-backup-$(shell date +%Y%m%d-%H%M%S).tar.gz \
		nginx/ volumes/ .env 2>/dev/null || true
	@echo "Backup created in backups/ directory"

watchtower: ## Start with Watchtower auto-updates
	@docker-compose --profile watchtower up -d

portainer: ## Start with Portainer management interface
	@docker-compose --profile management up -d

all-services: ## Start all services including optional ones
	@docker-compose --profile watchtower --profile management up -d
