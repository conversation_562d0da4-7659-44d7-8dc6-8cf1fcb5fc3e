services:
  haproxy-acme:
    image: flobernd/haproxy-acme-dns01:latest
    container_name: haproxy-acme-dns01
    restart: unless-stopped
    environment:
      - "HAPROXY_HTTP_PORT=80"
      - "HAPROXY_HTTPS_PORT=443"
      - "ACME_DEBUG=0"
      - "ACME_UPGRADE=1"
      - "ACME_CRON=1"
      - "ACME_MAIL=<EMAIL>"
      - "ACME_SERVER=letsencrypt_test"
      - "ACME_DOMAIN=domain.com *.domain.com"
      - "ACME_KEYLENGTH=ec-256"
      - "ACME_DNS_API=dns_cf"
      - "ACME_DNS_SLEEP=30"
      - "CF_Token=<redacted>"
      - "CF_Zone_ID=<redacted>"
      - "SERVER_ADDRESS=whoami"
      - "SERVER_PORT=80"
      - "SERVER_DIRECTIVES="
    volumes:
      - ./volume/acme:/var/lib/acme:rw
    ports:
      - 8080:80
      - 8443:443

  whoami:
    image: traefik/whoami
    container_name: whoami
    restart: unless-stopped
