name: publish docker image

on:
  push:
    tags:
      - v*

env:
  REGISTRY: ghcr.io
  IMAGE: jaimeqian/nginx-acme

jobs:
  build_and_publish_docker:
    name: publish docker image
    runs-on: ubuntu-latest

    # 这里用于定义 GITHUB_TOKEN 的权限
    permissions:
      packages: write
      contents: read

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Inject slug/short variables
        uses: rlespinasse/github-slug-action@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      # Create Docker Image in Github
      - name: Login to the GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}


      - name: Build And Push
        uses: docker/build-push-action@v3
        with:
          context: .
          push: true
          platforms: linux/amd64,linux/arm64
          tags: |
            ${{ env.REGISTRY }}/${{ env.IMAGE }}:${{ env.GITHUB_REF_NAME }}
            ${{ env.REGISTRY }}/${{ env.IMAGE }}:latest
            ${{ env.IMAGE }}:${{ env.GITHUB_REF_NAME }}
            ${{ env.IMAGE }}:latest
