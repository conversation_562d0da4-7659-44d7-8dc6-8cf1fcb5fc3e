# Homelab acme.sh Docker Image

[![Build](https://github.com/tuxgalhomelab/docker-image-acme/actions/workflows/build.yml/badge.svg)](https://github.com/tuxgalhomelab/docker-image-acme/actions/workflows/build.yml) [![Lint](https://github.com/tuxgalhomelab/docker-image-acme/actions/workflows/lint.yml/badge.svg)](https://github.com/tuxgalhomelab/docker-image-acme/actions/workflows/lint.yml)

The docker image used for running [`acme.sh`](https://acme.sh) in tuxgal's Homelab setup.
