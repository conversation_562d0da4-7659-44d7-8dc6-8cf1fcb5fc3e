global
    log stdout format raw local0
    maxconn 4096
    nbthread 1
    daemon
    tune.ssl.default-dh-param 2048

defaults
    log     global
    mode    http
    option  httplog
    option  dontlognull
    option  http-keep-alive
    option  forwardfor
    retries 3
    timeout connect 5000
    timeout client 50000
    timeout server 50000

frontend LAN_Frontend
    bind *:80
    bind *:443 ssl crt /etc/haproxy/certs/yourdomain.com/fullchain.cer ssl-min-ver TLSv1.3
    mode http
    log global
    option http-keep-alive
    option forwardfor

    # Redirect HTTP to HTTPS
    http-request redirect scheme https unless { ssl_fc }

    # Set X-Forwarded-Proto header
    http-request set-header X-Forwarded-Proto https if { ssl_fc }
    http-request set-header X-Forwarded-Proto http if !{ ssl_fc }

    # ACLs for each host
    acl host_service1 hdr(host) -i service1.example.com
    acl host_service2 hdr(host) -i service2.example.com
    # Add more ACLs as needed

    # Use backends based on ACLs
    use_backend service1_backend if host_service1
    use_backend service2_backend if host_service2
    # Add more backends as needed

    # Default backend
    default_backend default_backend

frontend WAN_Frontend
    bind *:10443 ssl crt /etc/haproxy/certs/yourdomain.com/fullchain.cer ssl-min-ver TLSv1.3
    mode http
    log global
    option http-keep-alive
    option forwardfor

    # Set X-Forwarded-Proto header
    http-request set-header X-Forwarded-Proto https if { ssl_fc }
    http-request set-header X-Forwarded-Proto http if !{ ssl_fc }

    # ACLs for WAN
    acl host_wan_service hdr(host) -i wan-service.example.com
    use_backend wan_service_backend if host_wan_service

    # Default backend (optional)
    default_backend default_backend

# Backends
backend service1_backend
    server service1 ************:8080

backend service2_backend
    server service2 ************:8080

backend wan_service_backend
    server wan_service ************:8443 ssl verify none

backend default_backend
    server default_server ************0:80
