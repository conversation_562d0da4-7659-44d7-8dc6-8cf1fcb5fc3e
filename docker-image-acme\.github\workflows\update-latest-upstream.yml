---
name: Update Latest Upstream

on:
  schedule:
    - cron: '0 */3 * * *'
  workflow_dispatch:

permissions:
  contents: write
  pull-requests: write

jobs:
  update-latest-upstream:
    name: Update Latest Upstream
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Fetch full annotated tags metadata
        run: git fetch --force --tags

      - name: Set up makesystem
        run: make makesystem_install

      - name: Configure git author info
        run: |
          git config user.name 'tuxgal'
          git config user.email '<EMAIL>'

      - name: Update Latest Upstream version locally
        run: make update_latest_upstream

      - uses: tibdex/github-app-token@v2
        id: generate-token
        with:
          app_id: ${{ secrets.UPDATE_LATEST_UPSTREAM_BOT_APP_ID }}
          private_key: ${{ secrets.UPDATE_LATEST_UPSTREAM_BOT_APP_PRIVATE_KEY }}

      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v7
        with:
          token: ${{ steps.generate-token.outputs.token }}
          branch: update-latest-upstream-bot/patch
          delete-branch: true
          title: |
            Update to the latest upstream version
          body: |
            Automated change generated by the Update Latest Upstream Bot.
