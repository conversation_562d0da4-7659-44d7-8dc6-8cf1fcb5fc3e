# Environment configuration for nginx-acme docker-compose setup

# Domain configuration
DOMAINS=example.com,www.example.com
EMAIL=<EMAIL>

# ACME server configuration
# Options: letsencrypt, letsencrypt_test, zerossl
ACME_SERVER=letsencrypt_test

# Auto-upgrade acme.sh
AUTO_UPGRADE=1

# Timezone
TZ=UTC

# Watchtower configuration (optional)
WATCHTOWER_SCHEDULE=0 0 4 * * *
WATCHTOWER_NOTIFICATIONS=

# DNS API credentials (for DNS-01 challenge)
# Uncomment and configure based on your DNS provider

# Cloudflare
# CF_Token=your_cloudflare_api_token
# CF_Account_ID=your_cloudflare_account_id
# CF_Zone_ID=your_cloudflare_zone_id

# AWS Route53
# AWS_ACCESS_KEY_ID=your_aws_access_key
# AWS_SECRET_ACCESS_KEY=your_aws_secret_key

# Google Cloud DNS
# GCE_PROJECT=your_gcp_project_id
# GCE_SERVICE_ACCOUNT_FILE_PATH=/path/to/service-account.json

# DigitalOcean
# DO_API_KEY=your_digitalocean_api_key

# Namecheap
# NAMECHEAP_USERNAME=your_namecheap_username
# NAMECHEAP_API_KEY=your_namecheap_api_key

# GoDaddy
# GD_Key=your_godaddy_api_key
# GD_Secret=your_godaddy_api_secret
