#!/usr/bin/env bash
set -E -e -o pipefail

# Add repo specific metadata here.

BASE_IMAGE_CONFIG_KEY_PREFIX="BASE_IMAGE"

RELEASE_PACKAGE_NAME="acme.sh"
RELEASE_TAG_PACKAGE_NAME="acme.sh"

UPSTREAM_PACKAGE_NAME="acme.sh"
UPSTREAM_VERSION_CONFIG_KEY="ACME_VERSION"
UPSTREAM_CHECKSUM_CONFIG_KEY="ACME_SHA256_CHECKSUM"
UPSTREAM_GIT_REPO="https://github.com/acmesh-official/acme.sh"

TEST_TYPE="foreground"
TEST_CONTAINER_TYPE="acme.sh"

current_upstream_version() {
    get_config_arg ${UPSTREAM_VERSION_CONFIG_KEY:?}
}

latest_upstream_version() {
    git_remote_repo_latest_tag "${UPSTREAM_GIT_REPO:?}"
}

update_latest_upstream_version() {
    local cur_ver="${1:?}"
    local latest_ver="${2:?}"
    local sha256_checksum="$(github_repo_archive_sha256_checksum ${UPSTREAM_GIT_REPO:?} ${latest_ver:?})"
    echo "Updating ${UPSTREAM_PACKAGE_NAME:?} ${UPSTREAM_VERSION_CONFIG_KEY:?} '${cur_ver:?}' -> '${latest_ver:?}'"
    set_config_arg "${UPSTREAM_VERSION_CONFIG_KEY:?}" "${latest_ver:?}"
    set_config_arg "${UPSTREAM_CHECKSUM_CONFIG_KEY:?}" "${sha256_checksum:?}"
    git add ${ARGS_FILE:?}
}

package_current_release_version() {
    current_upstream_version
}

test_start_container() {
    local container_name="${1:?}"
    docker run \
        --name ${container_name:?} \
        --rm \
        ${IMAGE:?} \
        acme.sh --list
}
