
server {
    listen 80;
    listen 443 ssl http2;
    server_name proxy.example.com;  # 将全部proxy.example.com替换成使用的域名
    
    access_log  /var/log/nginx/proxy.example.com.access.log main;
    error_log /var/log/nginx/proxy.example.com.error.log;

    #SSL-START SSL related configuration, do NOT delete or modify the next line of commented-out 404 rules
    #HTTP_TO_HTTPS_START
    if ($server_port !~ 443){
        rewrite ^(/.*)$ https://$host$1 permanent;
    }
    #HTTP_TO_HTTPS_END
    ssl_certificate    /nginx-conf/certs/proxy.example.com/fullchain.pem;
    ssl_certificate_key    /nginx-conf/certs/proxy.example.com/privkey.pem;
    ssl_protocols TLSv1.1 TLSv1.2 TLSv1.3;
    ssl_ciphers EECDH+CHACHA20:EECDH+CHACHA20-draft:EECDH+AES128:RSA+AES128:EECDH+AES256:RSA+AES256:EECDH+3DES:RSA+3DES:!MD5;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    add_header Strict-Transport-Security "max-age=31536000";
    error_page 497  https://$host$request_uri;

    #SSL-END    

    # 转发http
    location / {
        proxy_pass http://127.0.0.1:8080;  # 替换成你的服务器地址和端口
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header REMOTE-HOST $remote_addr;

    }

    # 转发webstock
    #location /websocket/ {
      #proxy_pass  http://127.0.0.1:8080/websocket/;  # 替换成你的服务器地址和端口
      #proxy_http_version 1.1;
      #proxy_pass_header Authorization;
      #proxy_set_header Upgrade "websocket";
      #proxy_set_header Connection "upgrade";
      #proxy_set_header Host $host;
      #proxy_set_header X-Real-IP $remote_addr;
      #proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      #proxy_set_header X-Forwarded-Proto https; # 如果您在反向代理上启用了 HTTPS
    #}

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /nginx-conf/wwwroot/default;
    }
    # proxy the PHP scripts to Apache listening on 127.0.0.1:80
    #
    #location ~ \.php$ {
    #    proxy_pass   http://127.0.0.1;
    #}

    # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
    #
    #location ~ \.php$ {
    #    root           html;
    #    fastcgi_pass   127.0.0.1:9000;
    #    fastcgi_index  index.php;
    #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;
    #    include        fastcgi_params;
    #}

    # deny access to .htaccess files, if Apache's document root
    # concurs with nginx's one
    #
    #location ~ /\.ht {
    #    deny  all;
    #}
}

