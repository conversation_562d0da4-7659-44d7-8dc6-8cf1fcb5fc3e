# Nginx with Automated ACME.sh SSL Certificates

This Docker Compose setup provides an nginx web server with automated SSL certificate management using acme.sh. It supports both HTTP-01 and DNS-01 ACME challenges for Let's Encrypt, ZeroSSL, and other ACME-compatible certificate authorities.

## Features

- 🔒 Automated SSL certificate issuance and renewal
- 🌐 Support for multiple domains
- 🔄 Automatic certificate renewal via cron
- 📊 Optional container management with Portainer
- 🔄 Optional automatic updates with Watchtower
- 🛡️ Security-hardened nginx configuration
- 📝 Comprehensive logging
- 🚀 Easy setup and deployment

## Quick Start

1. **Clone and setup:**
   ```bash
   git clone <your-repo>
   cd <your-repo>
   chmod +x scripts/*.sh
   ./scripts/setup.sh
   ```

2. **Configure environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your domain and email
   nano .env
   ```

3. **Start services:**
   ```bash
   docker-compose up -d
   ```

4. **Issue SSL certificate:**
   ```bash
   # HTTP-01 challenge (domain must point to your server)
   ./scripts/issue-cert.sh yourdomain.com
   
   # DNS-01 challenge (requires DNS API credentials)
   ./scripts/issue-cert.sh yourdomain.com dns_cf
   ```

5. **Configure nginx:**
   ```bash
   cp nginx/conf.d/example.conf.template nginx/conf.d/yourdomain.conf
   # Edit the configuration file with your domain
   nano nginx/conf.d/yourdomain.conf
   docker-compose restart nginx-acme
   ```

## Directory Structure

```
.
├── docker-compose.yml          # Main compose file
├── .env                       # Environment variables
├── nginx/
│   ├── nginx.conf            # Main nginx configuration
│   ├── conf.d/               # Virtual host configurations
│   ├── html/                 # Web content
│   └── ssl/                  # SSL certificates
├── logs/
│   └── nginx/                # Nginx logs
├── volumes/
│   └── acme-data/            # ACME.sh data and certificates
└── scripts/
    ├── setup.sh              # Initial setup script
    └── issue-cert.sh         # Certificate issuance script
```

## Configuration

### Environment Variables (.env)

| Variable | Description | Default |
|----------|-------------|---------|
| `DOMAINS` | Comma-separated list of domains | `example.com` |
| `EMAIL` | Email for ACME registration | `<EMAIL>` |
| `ACME_SERVER` | ACME server (letsencrypt, letsencrypt_test, zerossl) | `letsencrypt_test` |
| `AUTO_UPGRADE` | Auto-upgrade acme.sh | `1` |
| `TZ` | Timezone | `UTC` |

### DNS API Credentials

For DNS-01 challenges, add the appropriate API credentials to your `.env` file:

**Cloudflare:**
```bash
CF_Token=your_cloudflare_api_token
CF_Account_ID=your_cloudflare_account_id
```

**AWS Route53:**
```bash
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
```

**DigitalOcean:**
```bash
DO_API_KEY=your_digitalocean_api_key
```

## Certificate Management

### Issue New Certificate

```bash
# HTTP-01 challenge
./scripts/issue-cert.sh yourdomain.com

# DNS-01 challenge
./scripts/issue-cert.sh yourdomain.com dns_cf
```

### Manual Certificate Operations

```bash
# List all certificates
docker exec acme-sh acme.sh --list

# Check certificate info
docker exec acme-sh acme.sh --info -d yourdomain.com

# Force renewal
docker exec acme-sh acme.sh --renew -d yourdomain.com --force

# Revoke certificate
docker exec acme-sh acme.sh --revoke -d yourdomain.com
```

### Automatic Renewal

Certificates are automatically renewed via cron job in the acme-sh container. The renewal check runs every hour, and certificates are renewed when they have 30 days or less remaining.

## Nginx Configuration

### Adding a New Site

1. Copy the template:
   ```bash
   cp nginx/conf.d/example.conf.template nginx/conf.d/newsite.conf
   ```

2. Edit the configuration:
   ```nginx
   server {
       listen 443 ssl http2;
       server_name newsite.com;
       
       ssl_certificate /etc/nginx/ssl/newsite.com/fullchain.pem;
       ssl_certificate_key /etc/nginx/ssl/newsite.com/privkey.pem;
       
       # Your configuration here
   }
   ```

3. Issue certificate and reload:
   ```bash
   ./scripts/issue-cert.sh newsite.com
   docker exec nginx-acme nginx -s reload
   ```

## Optional Services

### Enable Watchtower (Auto-updates)

```bash
docker-compose --profile watchtower up -d
```

### Enable Portainer (Container Management)

```bash
docker-compose --profile management up -d
```

Access Portainer at: http://localhost:9000

## Troubleshooting

### Check Container Status
```bash
docker-compose ps
docker-compose logs nginx-acme
docker-compose logs acme-sh
```

### Test SSL Configuration
```bash
# Check certificate
openssl s_client -connect yourdomain.com:443 -servername yourdomain.com

# Test SSL rating
curl -s "https://api.ssllabs.com/api/v3/analyze?host=yourdomain.com"
```

### Common Issues

1. **Certificate issuance fails:**
   - Ensure domain points to your server (for HTTP-01)
   - Check DNS API credentials (for DNS-01)
   - Verify firewall allows ports 80/443

2. **Nginx fails to start:**
   - Check nginx configuration syntax: `docker exec nginx-acme nginx -t`
   - Ensure SSL certificate files exist

3. **Certificate not auto-renewing:**
   - Check acme-sh cron logs: `docker exec acme-sh crontab -l`
   - Verify renewal hook is working

## Security Considerations

- Keep your `.env` file secure and never commit it to version control
- Regularly update container images
- Monitor certificate expiration
- Use strong SSL/TLS configuration (included by default)
- Consider implementing fail2ban for additional protection

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.
