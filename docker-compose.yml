version: '3.9'

services:
  # Nginx with integrated acme.sh for automated SSL certificates
  nginx-acme:
    image: nginx:alpine
    container_name: nginx-acme
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      # Nginx configuration
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:rw
      - ./nginx/html:/usr/share/nginx/html:rw
      # SSL certificates
      - ./nginx/ssl:/etc/nginx/ssl:rw
      - acme-data:/root/.acme.sh:rw
      # Logs
      - ./logs/nginx:/var/log/nginx:rw
    environment:
      # Domain configuration
      - DOMAINS=${DOMAINS:-example.com}
      - EMAIL=${EMAIL:-<EMAIL>}
      # ACME server (letsencrypt, letsencrypt_test, zerossl)
      - ACME_SERVER=${ACME_SERVER:-letsencrypt}
      # Certificate renewal hook
      - RELOAD_CMD=nginx -s reload
    depends_on:
      - acme-sh
    networks:
      - acme-network
    labels:
      - "traefik.enable=false"

  # Standalone acme.sh service for certificate management
  acme-sh:
    image: neilpang/acme.sh:latest
    container_name: acme-sh
    restart: unless-stopped
    command: daemon
    volumes:
      # Shared certificate storage
      - acme-data:/acme.sh:rw
      - ./nginx/ssl:/nginx-ssl:rw
      # Docker socket for nginx reload (optional)
      - /var/run/docker.sock:/var/run/docker.sock:ro
    environment:
      # Default CA server
      - AUTO_UPGRADE=${AUTO_UPGRADE:-1}
      - CERT_HOME=/acme.sh
      # Email for registration
      - ACCOUNT_EMAIL=${EMAIL:-<EMAIL>}
    networks:
      - acme-network
    labels:
      - "traefik.enable=false"

  # Optional: Watchtower for automatic container updates
  watchtower:
    image: containrrr/watchtower:latest
    container_name: watchtower
    restart: unless-stopped
    environment:
      - WATCHTOWER_SCHEDULE=${WATCHTOWER_SCHEDULE:-0 0 4 * * *}
      - WATCHTOWER_CLEANUP=true
      - WATCHTOWER_INCLUDE_RESTARTING=true
      - WATCHTOWER_NOTIFICATIONS=${WATCHTOWER_NOTIFICATIONS:-}
      - TZ=${TZ:-UTC}
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - acme-network
    profiles:
      - watchtower
    labels:
      - "traefik.enable=false"

  # Optional: Portainer for container management
  portainer:
    image: portainer/portainer-ce:latest
    container_name: portainer
    restart: unless-stopped
    ports:
      - "9000:9000"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - portainer-data:/data:rw
    networks:
      - acme-network
    profiles:
      - management
    labels:
      - "traefik.enable=false"

volumes:
  # Persistent storage for acme.sh certificates and configuration
  acme-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/volumes/acme-data

  # Portainer data storage
  portainer-data:
    driver: local

networks:
  acme-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/24
          gateway: **********
