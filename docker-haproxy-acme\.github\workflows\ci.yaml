name: CI

on:
  push:
    branches:
      - master
  pull_request:
    types: [opened, synchronize, reopened]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  BASE_IMAGE: haproxy:2.9-bookworm
  REGIS<PERSON><PERSON>_IMAGE_HAPROXY_ACME: localhost:5000/flobernd/haproxy-acme
  REGISTRY_IMAGE_HAPROXY_ACME_HTTP01: localhost:5000/flobernd/haproxy-acme-http01
  REGISTRY_IMAGE_HAPROXY_ACME_DNS01: localhost:5000/flobernd/haproxy-acme-dns01
  REGISTRY_IMAGE_HAPROXY_ACME_TLSALPN01: localhost:5000/flobernd/haproxy-acme-tlsalpn01

jobs:
  build:
    name: CI
    runs-on: ubuntu-latest

    services:
      registry:
        image: registry:2
        ports:
          - 5000:5000

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver-opts: network=host

      - name: Extract metadata for 'haproxy-acme'
        id: meta-haproxy-acme
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY_IMAGE_HAPROXY_ACME }}
          # flavor: |
          #   latest=false
          # tags: |
          #   type=ref,event=branch
          #   type=ref,event=pr
          #   type=semver,pattern={{version}}
          #   type=semver,pattern={{major}}.{{minor}}

      - name: Build 'haproxy-acme'
        id: build-haproxy-acme
        uses: docker/build-push-action@v5
        with:
          context: ./haproxy-acme/data
          cache-from: type=gha
          cache-to: type=gha,mode=max
          push: true
          # platforms: linux/386,linux/amd64,linux/arm/v5,linux/arm/v7,linux/arm64,linux/mips64le,linux/ppc64le,linux/s390x
          build-args: |
            BASE_IMAGE=${{ env.BASE_IMAGE }}
          tags: ${{ steps.meta-haproxy-acme.outputs.tags }}
          labels: ${{ steps.meta-haproxy-acme.outputs.labels }}
          annotations: ${{ steps.meta-haproxy-acme.outputs.annotations }}

      - name: Extract metadata for 'haproxy-acme-http01'
        id: meta-haproxy-acme-http01
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY_IMAGE_HAPROXY_ACME_HTTP01 }}
          # flavor: |
          #   latest=false
          # tags: |
          #   type=ref,event=branch
          #   type=ref,event=pr
          #   type=semver,pattern={{version}}
          #   type=semver,pattern={{major}}.{{minor}}

      - name: Build 'haproxy-acme-http01'
        id: build-haproxy-acme-http01
        uses: docker/build-push-action@v5
        with:
          context: ./haproxy-acme-http01/data
          cache-from: type=gha
          cache-to: type=gha,mode=max
          push: true
          # platforms: linux/386,linux/amd64,linux/arm/v5,linux/arm/v7,linux/arm64,linux/mips64le,linux/ppc64le,linux/s390x
          build-args: |
            BASE_IMAGE=${{ fromJSON(steps.build-haproxy-acme.outputs.metadata)['image.name'] }}
          tags: ${{ steps.meta-haproxy-acme-http01.outputs.tags }}
          labels: ${{ steps.meta-haproxy-acme-http01.outputs.labels }}
          annotations: ${{ steps.meta-haproxy-acme-http01.outputs.annotations }}

      - name: Extract metadata for 'haproxy-acme-dns01'
        id: meta-haproxy-acme-dns01
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY_IMAGE_HAPROXY_ACME_DNS01 }}
          # flavor: |
          #   latest=false
          # tags: |
          #   type=ref,event=branch
          #   type=ref,event=pr
          #   type=semver,pattern={{version}}
          #   type=semver,pattern={{major}}.{{minor}}

      - name: Build 'haproxy-acme-dns01'
        id: build-haproxy-acme-dns01
        uses: docker/build-push-action@v5
        with:
          context: ./haproxy-acme-dns01/data
          cache-from: type=gha
          cache-to: type=gha,mode=max
          push: true
          # platforms: linux/386,linux/amd64,linux/arm/v5,linux/arm/v7,linux/arm64,linux/mips64le,linux/ppc64le,linux/s390x
          build-args: |
            BASE_IMAGE=${{ fromJSON(steps.build-haproxy-acme.outputs.metadata)['image.name'] }}
          tags: ${{ steps.meta-haproxy-acme-dns01.outputs.tags }}
          labels: ${{ steps.meta-haproxy-acme-dns01.outputs.labels }}
          annotations: ${{ steps.meta-haproxy-acme-dns01.outputs.annotations }}

      - name: Extract metadata for 'haproxy-acme-tlsalpn01'
        id: meta-haproxy-acme-tlsalpn01
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY_IMAGE_HAPROXY_ACME_TLSALPN01 }}
          # flavor: |
          #   latest=false
          # tags: |
          #   type=ref,event=branch
          #   type=ref,event=pr
          #   type=semver,pattern={{version}}
          #   type=semver,pattern={{major}}.{{minor}}

      - name: Build 'haproxy-acme-tlsalpn01'
        id: build-haproxy-acme-tlsalpn01
        uses: docker/build-push-action@v5
        with:
          context: ./haproxy-acme-tlsalpn01/data
          cache-from: type=gha
          cache-to: type=gha,mode=max
          push: true
          # platforms: linux/386,linux/amd64,linux/arm/v5,linux/arm/v7,linux/arm64,linux/mips64le,linux/ppc64le,linux/s390x
          build-args: |
            BASE_IMAGE=${{ fromJSON(steps.build-haproxy-acme.outputs.metadata)['image.name'] }}
          tags: ${{ steps.meta-haproxy-acme-tlsalpn01.outputs.tags }}
          labels: ${{ steps.meta-haproxy-acme-tlsalpn01.outputs.labels }}
          annotations: ${{ steps.meta-haproxy-acme-tlsalpn01.outputs.annotations }}
