user nginx;
worker_processes auto;

error_log /var/log/nginx/error.log notice;
pid /var/run/nginx.pid;


events {
    worker_connections 10240;
}


http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
    '$status $body_bytes_sent "$http_referer" '
    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    sendfile on;
    #tcp_nopush     on;
    # 上传文件大小限制
    client_max_body_size 4096m;
    client_body_buffer_size 1024k;
    fastcgi_intercept_errors on;

    keepalive_timeout 65;

    #gzip  on;

    # websocket 转发需要加下这个
    map $http_upgrade $connection_upgrade {
        default upgrade;
        '' close;
    }

    include /nginx-conf/conf.d/*.conf;
}

include /nginx-conf/conf.d/stream.d/*.conf;
